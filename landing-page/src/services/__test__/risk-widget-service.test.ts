import { createHeaders } from '../user-service';
import { FetchParams } from '../../types/types';

// Mock environment variables before importing the service
process.env.API_RISK_ASSESSMENT_URL = 'http://test-api/risk';
process.env.VESSEL_HOST = 'http://test-api/vessel';

// Now import the service after environment variables are set
import { getVessels3, fetchVesselOwnerships } from '../risk-widget-service';

jest.mock('../user-service', () => ({
  createHeaders: jest.fn().mockResolvedValue({
    Authorization: 'Bearer test-token',
  }),
}));

// Mock fetch
global.fetch = jest.fn();

describe('risk-widget-service', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getVessels3', () => {
    const mockSuccessResponse = {
      data: [
        {
          vessel_name: 'Test Vessel',
          ra_level: 1,
          task_requiring_ra: 'Test Task',
          vessel_ownership_id: 'own-1',
          id: 'risk-1',
          vessel_id: 'vessel-1',
        },
      ],
      last_updated_on: '2025-08-12T07:09:23Z',
    };

    it('should handle null ra_level', async () => {
      const mockFetch = global.fetch as jest.Mock;
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () =>
          Promise.resolve({
            data: [
              {
                vessel_name: 'Test Vessel',
                ra_level: null,
                task_requiring_ra: 'Test Task',
                vessel_ownership_id: 'own-1',
                id: 'risk-1',
                vessel_id: 'vessel-1',
              },
            ],
            last_updated_on: '2025-08-12T07:09:23Z',
          }),
      });

      const params: FetchParams = { page: 1, limit: 10 };
      const result = await getVessels3(params);

      expect(result.data[0].type).toBe('Unassigned');
      expect(result.data[0].vesselData[1]).toBe('Unassigned');
    });
  });

  describe('fetchVesselOwnerships', () => {
    const mockVesselResponse = {
      results: [
        {
          name: 'Vessel 1',
          vessel_id: 'v1',
          id: 'own1',
        },
        {
          name: 'Vessel 2',
          vessel_id: 'v2',
          id: 'own2',
        },
      ],
    };

    it('should handle API error', async () => {
      const mockFetch = global.fetch as jest.Mock;
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
      });

      await expect(fetchVesselOwnerships()).rejects.toThrow(
        'Failed to fetch vessel ownerships: 404',
      );
    });

    it('should handle invalid response format', async () => {
      const mockFetch = global.fetch as jest.Mock;
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ results: 'not an array' }),
      });

      await expect(fetchVesselOwnerships()).rejects.toThrow(
        "Unexpected API response format: 'results' is not an array",
      );
    });
  });
});
