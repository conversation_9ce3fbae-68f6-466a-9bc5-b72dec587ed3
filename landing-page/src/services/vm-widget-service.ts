import { createHeaders } from 'src/services/user-service';
import {
  FetchParams,
  IProjectListResponse,
  IRiskAssessmentResponse,
  ItineraryApiResponse,
  OfrStatsApiResponse,
  RaLevel,
  RAStatus,
  TransformedIRiskAssessmentResponse,
  SurveyCertificateApiResponse,
  TransformedRisk,
  TransformedRiskAssessment,
  VesselGroup,
  VesselOption,
  SurveyCertificateWithType,
  WidgetsApiResponse,
} from '../types/types';
import { AxiosResponse } from 'axios';
import { httpClient } from 'src/config/http-client';
import { mapCertificateType } from 'src/utils/utility';

const raLevelMap: Record<number, keyof typeof RaLevel> = {
  1: 'ROUTINE',
  2: 'SPECIAL',
  3: 'CRITICAL',
  4: 'LEVEL_1_RA',
};
// Handle null case separately in code logic

const raLevelLabels: Record<keyof typeof RaLevel, string> = {
  ROUTINE: 'Routine',
  SPECIAL: 'Special',
  CRITICAL: 'Critical',
  LEVEL_1_RA: 'Level 1 RA',
  Unassigned: 'Unassigned',
};
const statusMap: Record<number, keyof typeof RAStatus> = {
  1: 'DRAFT',
  2: 'PENDING',
  3: 'APPROVED',
  4: 'REJECTED',
  5: 'APPROVED_WITH_CONDITION',
};

const statusLabels: Record<keyof typeof RAStatus, string> = {
  DRAFT: 'Draft',
  PENDING: 'Pending',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  APPROVED_WITH_CONDITION: 'Approved with Condition',
};

const { API_RISK_ASSESSMENT_URL, VESSEL_HOST, OWNER_PREFERENCES_HOST, API_WIDGET_URL } =
  process.env;

// no longer in use, depreciate after removing the RA widget (earlier one)
export async function getVessels3(
  params: FetchParams & { refetch?: boolean }, // Accept refetch param
): Promise<IProjectListResponse<TransformedRisk>> {
  const url = `${API_RISK_ASSESSMENT_URL}/risk-dashboard`;
  const headers = await createHeaders();
  headers['Content-Type'] = 'application/json';

  // Prepare body, add refetch:true if params.refetch is true
  const body: Record<string, any> = {};
  body.refresh = true;

  const res = await fetch(url, {
    method: 'POST',
    headers,
    body: JSON.stringify(body),
  });

  if (!res.ok) {
    const errorBody = await res.text();
    throw new Error(`Failed to fetch from ${url}. Status: ${res.status}. Body: ${errorBody}`);
  }

  const apiResponse: IRiskAssessmentResponse = await res.json();

  const transformedData: TransformedRisk[] = apiResponse.data.map((risk) => {
    const raLevelKey =
      risk.ra_level !== null && raLevelMap[risk.ra_level]
        ? raLevelMap[risk.ra_level]
        : 'Unassigned';

    const raLevelLabel = raLevelLabels[raLevelKey];

    return {
      name: risk.vessel_name || 'Unknown Vessel',
      type: raLevelKey,
      vesselData: [risk.task_requiring_ra, raLevelLabel],
      vessel_ownership_id: risk.vessel_ownership_id,
      risk_id: risk.id ?? null,
      vessel_id: risk.vessel_id,
    };
  });

  // Pagination (client-side slicing)
  const totalItems = transformedData.length;
  const page = params.page || 1;
  const limit = params.limit || 10;
  const totalPages = Math.ceil(totalItems / limit);
  const paginatedData = transformedData.slice((page - 1) * limit, page * limit);

  return {
    data: paginatedData,
    lastUpdated: apiResponse.last_updated_on,
    pagination: {
      totalItems,
      totalPages,
      page,
      pageSize: limit,
    },
  };
}

// fetching all risks
export const getVesselsRA = async (
  params: FetchParams & { refetch?: boolean },
): Promise<AxiosResponse<TransformedIRiskAssessmentResponse>> => {
  try {
    const url = `${API_RISK_ASSESSMENT_URL}/risk-dashboard`;
    const headers = await createHeaders();

    // Prepare body: only include 'refresh: true' if params.refetch is true
    const body = { refresh: true };

    // Use httpClient.post to simplify the API call
    const apiResponse = await httpClient.post<IRiskAssessmentResponse>(url, body, { headers });

    // --- Start: Client-side data transformation and pagination logic (from your original code) ---
    const transformedData: TransformedRiskAssessment[] = apiResponse.data.data.map((risk) => {
      const raLevelKey =
        risk.ra_level !== null && raLevelMap[risk.ra_level]
          ? raLevelMap[risk.ra_level]
          : 'Unassigned';

      const raLevelLabel = raLevelLabels[raLevelKey];

      return {
        vessel_name: risk.vessel_name || 'Unknown Vessel',
        id: risk.id ?? null,
        vessel_id: risk.vessel_id,
        vessel_ownership_id: risk.vessel_ownership_id,
        ra_level: raLevelLabel,
        task_requiring_ra: risk.task_requiring_ra,
      };
    });

    // Pagination (client-side slicing)
    const totalItems = transformedData.length;
    const page = params.page || 1;
    const limit = params.limit || 10;
    const totalPages = Math.ceil(totalItems / limit);
    const paginatedData = transformedData.slice((page - 1) * limit, page * limit);
    // --- End: Data transformation logic ---

    // Return the processed data and pagination info in the expected format
    return {
      data: paginatedData,
      lastUpdated: apiResponse.data.last_updated_on,
      pagination: {
        totalItems,
        totalPages,
        page,
        pageSize: limit,
      },
    } as unknown as AxiosResponse<IRiskAssessmentResponse>; // Type assertion to match return type
  } catch (error) {
    // Handle errors from the httpClient or data processing
    console.error('Error fetching risk assessments:', error);
    throw error;
  }
};

export async function fetchVesselOwnerships(): Promise<VesselGroup[]> {
  const headers = await createHeaders();

  const response = await fetch(
    `${VESSEL_HOST}/ownerships?order=created_at+desc&status=active&flatten=true&f=name&f=id&f=vessel.id&f=vessel_ownership.vessel_account_code_new`,
    {
      method: 'GET',
      headers: headers,
    },
  );

  if (!response.ok) {
    throw new Error(`Failed to fetch vessel ownerships: ${response.status}`);
  }

  const json = await response.json();
  const vesselArray = json?.results;

  if (!Array.isArray(vesselArray)) {
    throw new Error("Unexpected API response format: 'results' is not an array");
  }

  const vesselOptions: VesselOption[] = vesselArray
    .filter((item: any) => item.name && item.vessel_id)
    .map((item: any) => ({
      vessel_id: item.vessel_id,
      name: item.name,
      vessel_ownership_id: item.id,
      vessel_account_code_new: item.vessel_account_code_new,
    }));

  const vesselGroups1: VesselGroup[] = [
    {
      id: 1,
      title: '',
      vessels: vesselOptions,
    },
  ];

  return vesselGroups1;
}

export const getItineraries = async (): Promise<AxiosResponse<ItineraryApiResponse>> => {
  const limitParam = 'limit=5000';
  const offsetParam = 'offset=0';
  const queryParams = [limitParam, offsetParam].join('&');
  return httpClient.get(`${VESSEL_HOST}/itinerary?${queryParams}`, {
    headers: await createHeaders(),
  });
};

// Wrapper function for useInfiniteQuery compatibility
export const getItinerariesForTable = async (params: {
  page: number;
  limit: number;
  [key: string]: any;
}): Promise<IProjectListResponse<any>> => {
  try {
    const response = await getItineraries();
    const apiData = response.data;

    // Transform the API response to match IProjectListResponse format
    const totalItems = apiData.total;
    const totalPages = Math.ceil(totalItems / params.limit);

    // Client-side pagination since the API returns all results
    const startIndex = (params.page - 1) * params.limit;
    const endIndex = startIndex + params.limit;
    const paginatedData = apiData.results.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      lastUpdated: new Date().toISOString(), // API doesn't provide lastUpdated, so use current time
      pagination: {
        totalItems,
        totalPages,
        page: params.page,
        pageSize: params.limit,
      },
    };
  } catch (error) {
    console.error('Error fetching itineraries:', error);
    throw error;
  }
};

export const getOfrStats = async (): Promise<AxiosResponse<OfrStatsApiResponse>> => {
  return httpClient.get(`${OWNER_PREFERENCES_HOST}/reports/stats`, {
    headers: await createHeaders(),
  });
};

// --- Paginated Helper ---
export const getOfrStatsForTable = async (params: {
  page: number;
  limit: number;
  vesselCodes: number[];
  [key: string]: any;
}): Promise<IProjectListResponse<any>> => {
  try {
    const response = await getOfrStats();
    const apiData = response.data;

    const totalItems = apiData.results.length;
    const totalPages = Math.ceil(totalItems / params.limit);

    // Client-side pagination
    const startIndex = (params.page - 1) * params.limit;
    const endIndex = startIndex + params.limit;
    const paginatedData = apiData.results.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      lastUpdated: apiData.lastModified ?? new Date().toISOString(),
      pagination: {
        totalItems,
        totalPages,
        page: params.page,
        pageSize: params.limit,
      },
    };
  } catch (error) {
    console.error('Error fetching OFR stats:', error);
    throw error;
  }
};

export const getDeficiency = async (vesselIds: number[]): Promise<AxiosResponse<any>> => {
  try {
    const url = `${API_WIDGET_URL}/defects`;
    const headers = await createHeaders();

    const body = {
      vessel_ids: vesselIds ?? [],
    };

    const response = await httpClient.post<any>(url, body, { headers });
    return response;
  } catch (error: any) {
    console.error('Error fetching defect stats:', error.response?.data || error.message);
    throw error;
  }
};

export const getDeficienciesForTable = async (params: {
  page: number;
  limit: number;
  vesselIds: number[];
  [key: string]: any;
}): Promise<IProjectListResponse<any>> => {
  try {
    const response = await getDeficiency(params.vesselIds);
    const apiData = response.data.data;
    const defectsData = apiData.defects.data;
    const totalItems = apiData.defects.count;

    // --- Transformation Logic ---
    const transformedData = defectsData.map((item: any) => ({
      type: item.category || 'Uncategorized',
      vessel_ownership_id: null,
      vessel_name: item.vessel_name,
      name: item.vessel_name,
      not_accepted_by_office: item.not_accepted_by_office,
      overdue: item.overdue,
      due_within_30_days: item.with_in_30_days,
      ra_level: null,
      task_requiring_ra: null,
      id: item.vessel_id,
      vesselData: [item.overdue, item.with_in_30_days, item.others],
    }));

    // --- Pagination Logic ---
    const startIndex = (params.page - 1) * params.limit;
    const endIndex = startIndex + params.limit;
    const paginatedData = transformedData.slice(startIndex, endIndex);

    return {
      data: paginatedData,
      lastUpdated: new Date().toISOString(),
      pagination: {
        totalItems,
        totalPages: Math.ceil(totalItems / params.limit),
        page: params.page,
        pageSize: params.limit,
      },
      chartData: {
        openDeficiencies: {
          title: 'Open Deficiencies (Not accepted by office)',
          total: transformedData.reduce(
            (sum: number, item: any) => sum + item.not_accepted_by_office,
            0,
          ),
          data: [
            {
              label: 'Overdue',
              value: transformedData.reduce((sum: number, item: any) => sum + item.overdue, 0),
              color: '#d80e61',
            },
            {
              label: 'Due within 30 days',
              value: transformedData.reduce(
                (sum: number, item: any) => sum + item.due_within_30_days,
                0,
              ),
              color: '#fbc02d',
            },
            {
              label: 'Others',
              value: transformedData.reduce((sum: number, item: any) => sum + item.others, 0),
              color: '#27a527',
            },
          ],
        },
        closedDeficiencies: {
          title: 'Closed Deficiencies (Accepted by office)',
          total: transformedData.reduce(
            (sum: number, item: any) => sum + item.accepted_by_office,
            0,
          ),
          data: [
            {
              label: 'High',
              value: apiData.severity['Non Conformity']?.high || 0,
              color: '#d80e61',
            }, // Placeholder, adjust as needed
            {
              label: 'Medium',
              value: apiData.severity['Non Conformity']?.medium || 0,
              color: '#fbc02d',
            }, // Placeholder, adjust as needed
            { label: 'Low', value: apiData.severity['Non Conformity']?.low || 0, color: '#27a527' }, // Placeholder, adjust as needed
          ],
        },
      },
    };
  } catch (error) {
    console.error('Error fetching defect stats:', error);
    throw error;
  }
};

export const getSurveysAndCerts = async (
  vesselIds: number[],
): Promise<AxiosResponse<SurveyCertificateApiResponse>> => {
  return httpClient.post(
    `${VESSEL_HOST}/certificate/vessel/stats`,
    {
      vesselIds: vesselIds ?? [],
    },
    {
      headers: await createHeaders(),
    },
  );
};

// --- Paginated Helper ---
export const getSurveysAndCertsForTable = async (params: {
  page: number;
  limit: number;
  vesselIds: number[];
  [key: string]: any;
}): Promise<IProjectListResponse<any>> => {
  try {
    const response = await getSurveysAndCerts(params.vesselIds);
    const apiData = response.data;
    const totalItems = apiData.total;
    const paginatedData = apiData.results;

    // --- Transformation Logic ---
    const transformedData = paginatedData.map((item) => {
      // Map API fields to the mock data structure
      const transformedItem = {
        type: mapCertificateType(item.certificate_type),
        vessel_ownership_id: item.ownership_id,
        vessel_name: item.name,
        name: item.name,
        overdue: parseInt(item.overdue, 10),
        due_within_30_days: parseInt(item.due_within_30_days, 10),
        due_within_60_days: parseInt(item.due_within_60_days, 10),
        vessel_id: item.vessel_id,
        // Map the API's specific data points to the generic `vesselData` array
        vesselData: [
          parseInt(item.overdue, 10),
          parseInt(item.due_within_30_days, 10),
          parseInt(item.due_within_60_days, 10), // Assuming 'rest' is the third data point
        ],
        countforBarChart:
          parseInt(item.overdue, 10) +
          parseInt(item.due_within_30_days, 10) +
          parseInt(item.due_within_60_days, 10),
      };
      return transformedItem;
    });

    return {
      data: transformedData,
      lastUpdated: apiData.lastModified ?? new Date().toISOString(),
      pagination: {
        totalItems,
        totalPages: Math.ceil(totalItems / params.limit),
        page: params.page,
        pageSize: params.limit,
      },
    };
  } catch (error) {
    console.error('Error fetching vessel stats:', error);
    throw error;
  }
};

export const getWidgets = async (): Promise<AxiosResponse<WidgetsApiResponse>> => {
  const response = await httpClient.get<WidgetsApiResponse>(`${API_WIDGET_URL}/widget`, {
    headers: await createHeaders(),
  });

  // Sort results by default_position ascending
  response.data.results.sort((a, b) => a.default_position - b.default_position);

  return response;
};
