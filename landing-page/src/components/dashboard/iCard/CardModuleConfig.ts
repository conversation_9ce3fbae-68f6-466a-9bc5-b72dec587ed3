import { WidgetConstant } from './widget.constant';
import {
  fetchVesselOwnerships,
  getDeficienciesForTable,
  getItinerariesForTable,
  getOfrStatsForTable,
  getSurveysAndCertsForTable,
  getVesselsRA,
} from '../../../services/vm-widget-service';
import {
  getDefiniciencyColumns,
  getRiskAssessmentColumns,
  getItineraryColumns,
  getOfrColumns,
  getSurveysAndCertsColumns,
} from './ColumnConfig';
import { vesselGroups2 } from 'src/services/__mocks__/card-module-config';
import { IProjectListResponse } from 'src/types/types';

enum RaLevel {
  //   ROUTINE = 'ROUTINE',
  //   LEVEL_1_RA = 'LEVEL_1_RA',
  SPECIAL = 'SPECIAL',
  CRITICAL = 'CRITICAL',
  Unassigned = 'Unassigned',
}

const levelMapping: Record<string, string> = {
  //   [RaLevel.ROUTINE]: 'Routine',
  //   [RaLevel.LEVEL_1_RA]: 'Level_1_RA',
  [RaLevel.SPECIAL]: 'Special',
  [RaLevel.CRITICAL]: 'Critical',
  [RaLevel.Unassigned]: 'Unassigned',
};

const raLevels = [
  //   RaLevel.ROUTINE,
  //   RaLevel.LEVEL_1_RA,
  RaLevel.SPECIAL,
  RaLevel.CRITICAL,
  RaLevel.Unassigned,
];

const types = [
  'Statutory',
  'Important',
  'Ancillary',
  'Non Conformity',
  'Observation',
  'Defect',
  'Technical Follow-up',
];

// Mock fetch function with infinte data for demonstration
const mockFetchFn1 = async (params: {
  page: number;
  limit: number;
  [key: string]: any;
}): Promise<any> => {
  console.log('Fetching with params:', params);
  await new Promise((resolve) => setTimeout(resolve, 1000));
  const mockData = Array.from({ length: params.limit }, (_, i) => ({
    type: types[i % types.length],
    vessel_ownership_id: i,
    vessel_name: `Vessel ${i + (params.page - 1) * params.limit}`,
    //these below 4 are responsible for deficiency
    name: `Vessel ${i + (params.page - 1) * params.limit}`,
    not_accepted_by_office: i,
    overdue: i,
    due_within_30_days: i,
    //till
    ra_level: levelMapping[raLevels[i % raLevels.length]], // Or provide mock levels e.g. "High", "Medium", etc.
    task_requiring_ra: `Task for Vessel ask for Vesseask for Vesseask for Vesse${i}`,
    id: i,
    vesselData: [i, i, i],
  }));
  return {
    data: mockData,
    pagination: {
      totalItems: 1,
      totalPages: 1,
      page: params.page,
      pageSize: params.limit,
    },
  };
};

// Define a static data array with 5 items.
// New mock data object that mimics the actual API response structure
const newApiMockData ={
    "message": "Defect statistics retrieved successfully",
    "data": {
        "defects": {
            "data": [
                {
                    "vessel_id": 1236,
                    "vessel_name": "Mari Couva",
                    "category": "Non Conformity",
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 2,
                    "overdue": 2,
                    "with_in_30_days": 0,
                    "others": 0
                },
                {
                    "vessel_id": 1236,
                    "vessel_name": "Mari Couva",
                    "category": "Defect",
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 1,
                    "overdue": 1,
                    "with_in_30_days": 0,
                    "others": 0
                },
                {
                    "vessel_id": 1236,
                    "vessel_name": "Mari Couva",
                    "category": null,
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 3,
                    "overdue": 0,
                    "with_in_30_days": 0,
                    "others": 3
                },
                {
                    "vessel_id": 1352,
                    "vessel_name": "GW Dolphin",
                    "category": null,
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 8,
                    "overdue": 0,
                    "with_in_30_days": 0,
                    "others": 8
                },
                {
                    "vessel_id": 1352,
                    "vessel_name": "GW Dolphin",
                    "category": "Technical Follow Up",
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 1,
                    "overdue": 1,
                    "with_in_30_days": 0,
                    "others": 0
                },
                {
                    "vessel_id": 1362,
                    "vessel_name": "Karimata",
                    "category": "Defect",
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 6,
                    "overdue": 5,
                    "with_in_30_days": 0,
                    "others": 1
                },
                {
                    "vessel_id": 1362,
                    "vessel_name": "Karimata",
                    "category": null,
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 1,
                    "overdue": 0,
                    "with_in_30_days": 0,
                    "others": 1
                },
                {
                    "vessel_id": 1478,
                    "vessel_name": "LR1 Charm",
                    "category": null,
                    "accepted_by_office": 1,
                    "not_accepted_by_office": 9,
                    "overdue": 0,
                    "with_in_30_days": 0,
                    "others": 10
                },
                {
                    "vessel_id": 1478,
                    "vessel_name": "LR1 Charm",
                    "category": "NonConformity",
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 1,
                    "overdue": 0,
                    "with_in_30_days": 0,
                    "others": 1
                },
                {
                    "vessel_id": 1489,
                    "vessel_name": "Trigon Trader",
                    "category": "NonConformity",
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 4,
                    "overdue": 0,
                    "with_in_30_days": 0,
                    "others": 4
                },
                {
                    "vessel_id": 1828,
                    "vessel_name": "SSL SABARIMALAI",
                    "category": "Observation",
                    "accepted_by_office": 0,
                    "not_accepted_by_office": 2,
                    "overdue": 2,
                    "with_in_30_days": 0,
                    "others": 0
                }
            ],
            "count": 11
        },
        "severity": {
            "Non Conformity": {
                "low": 2,
                "medium": 0,
                "high": 0
            },
            "Defect": {
                "low": 2,
                "medium": 5,
                "high": 0
            },
            "Uncategorized": {
                "low": 0,
                "medium": 0,
                "high": 0
            },
            "Technical Follow Up": {
                "low": 0,
                "medium": 0,
                "high": 1
            },
            "NonConformity": {
                "low": 0,
                "medium": 0,
                "high": 0
            },
            "Observation": {
                "low": 2,
                "medium": 0,
                "high": 1
            }
        }
    }
};

// Transform the new API data to match the previous working mock data structure.
const transformedStaticData = newApiMockData.data.defects.data.map((item) => ({
  type: item.category || 'Uncategorized',
  vessel_ownership_id: null,
  vessel_name: item.vessel_name,
  name: item.vessel_name,
  not_accepted_by_office: item.not_accepted_by_office,
  overdue: item.overdue,
  due_within_30_days: item.with_in_30_days,
  ra_level: null,
  task_requiring_ra: null,
  id: item.vessel_id,
  vesselData: [item.overdue, item.with_in_30_days, item.others],
}));

// Calculate totals for Open Deficiencies
const totalOpenDeficiencies = newApiMockData.data.defects.data.reduce(
  (sum, item) => sum + item.not_accepted_by_office,
  0,
);
const totalOverdue = newApiMockData.data.defects.data.reduce((sum, item) => sum + item.overdue, 0);
const totalDueWithin30Days = newApiMockData.data.defects.data.reduce(
  (sum, item) => sum + item.with_in_30_days,
  0,
);
const totalOthers = newApiMockData.data.defects.data.reduce((sum, item) => sum + item.others, 0);
const totalClosedDeficiencies = newApiMockData.data.defects.data.reduce(
  (sum, item) => sum + item.accepted_by_office,
  0,
);

// Calculate totals for Closed Deficiencies by severity across all categories
const severityData = newApiMockData.data.severity;
const totalHighSeverity = Object.values(severityData).reduce(
  (sum, category) => sum + (category.high || 0),
  0,
);
const totalMediumSeverity = Object.values(severityData).reduce(
  (sum, category) => sum + (category.medium || 0),
  0,
);
const totalLowSeverity = Object.values(severityData).reduce(
  (sum, category) => sum + (category.low || 0),
  0,
);

const chartData = {
  openDeficiencies: {
    title: 'Open Deficiencies (Not accepted by office)',
    total: totalOpenDeficiencies,
    data: [
      { label: 'Overdue', value: totalOverdue, color: '#d80e61' },
      { label: 'Due within 30 days', value: totalDueWithin30Days, color: '#fbc02d' },
      { label: 'Others', value: totalOthers, color: '#27a527' },
    ],
  },
  closedDeficiencies: {
    title: 'Closed Deficiencies (Accepted by office)',
    total: totalClosedDeficiencies,
    data: [
      { label: 'High', value: totalHighSeverity, color: '#d80e61' },
      { label: 'Medium', value: totalMediumSeverity, color: '#fbc02d' },
      { label: 'Low', value: totalLowSeverity, color: '#27a527' },
    ],
  },
};

// Modify mockFetchFn to return the transformed data.
const mockFetchFn = async (params: {
  page: number;
  limit: number;
  [key: string]: any;
}): Promise<any> => {
  console.log('Fetching with params:', params);
  await new Promise((resolve) => setTimeout(resolve, 500));
  
  const startIndex = (params.page - 1) * params.limit;
  const endIndex = startIndex + params.limit;
  const paginatedData = transformedStaticData.slice(startIndex, endIndex);

  return {
    data: paginatedData,
    chartData: chartData,
    pagination: {
      totalItems: transformedStaticData.length,
      totalPages: Math.ceil(transformedStaticData.length / params.limit),
      page: params.page,
      pageSize: params.limit,
    },
  };
};

export const cardModuleConfigs: { [key: string]: any } = {
  [WidgetConstant.OWNER_FINANCIAL_REPORTING]: {
    title: 'Owner Financial Reporting',
    fetchFn1: getOfrStatsForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    columns: getOfrColumns,
    sizeKey: 'md',
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_code',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },

  [WidgetConstant.ITINERARY_ETA]: {
    title: 'Itinerary (ETA)',
    fetchFn1: getItinerariesForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px', // 200 or 300 or 350 should need to handle with an enum or interface to allow only these values
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    columns: getItineraryColumns,
    sizeKey: 'md',
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_id',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },

  [WidgetConstant.RISK_ASSESSMENT]: {
    title: 'Risk Assessment',
    fetchFn1: getVesselsRA,
    // Pass the function here, not the result of the call
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
      {
        placeholder: 'Level of R.A.',
        width: '300px',
        groups: vesselGroups2,
        isSearchBoxVisible: false,
        isSelectAllVisible: false,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Vessel', 'Header 1', 'Header 2', 'Header 3'],
      badgeColors: ['red', 'blue', 'green'],
    },
    sizeKey: 'md',
    columns: getRiskAssessmentColumns,
    visibleConfig: {
      IsiconRenderVisible: false,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: false,
      IsAllTabVisible: false,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_id',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'list',
    },
  },

  [WidgetConstant.DEFICIENCIES]: {
    title: 'Deficiencies',
    fetchFn1: getDeficienciesForTable,
    // Pass the function here, not the result of the call
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships, // Pass the fetch function
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['Non Conformity', 'Observation', 'Defect', 'Technical Follow-up'],
      tableHeaders: ['Overdue', 'Due within 30 Days', 'others'],
      badgeColors: ['#d80e61', '#fbc02d', '#27a527'],
      chartData: chartData,
    },
    sizeKey: 'md',
    columns: getDefiniciencyColumns,
    visibleConfig: {
      IsiconRenderVisible: true,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: true,
      IsAllTabVisible: true,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_id',
    },
    componentView: {
      gridComponent: 'pie',
      defaultComponent: 'grid',
    },
  },

  [WidgetConstant.SURVEYS_CERTIFICATES]: {
    title: 'Surveys and Certificates',
    fetchFn1: getSurveysAndCertsForTable,
    multiVesselSelects: [
      {
        placeholder: 'All Vessels',
        width: '300px',
        groups: fetchVesselOwnerships,
        isSearchBoxVisible: true,
        isSelectAllVisible: true,
      },
    ],
    staticData: {
      tabs: ['All', 'Statutory', 'Important', 'Ancillary'],
      tableHeaders: ['Overdue', 'Due within 30 Days', 'Due within 60 Days'],
      badgeColors: ['#d80e61', '#fbc02d', '#27a527'],
      barChartMaxRange: 250,
    },
    sizeKey: 'md',
    columns: getSurveysAndCertsColumns,
    visibleConfig: {
      IsiconRenderVisible: true,
      IsenLargeIconVisible: true,
      IsVesselSelectVisible: true,
      IsAlltabsVisible: true,
      IsAllTabVisible: true,
      IsLastUpdatedVisible: true,
      IsRefereshIconVisible: false,
      IsActionColumnVisible: true,
      vesselSelectPosition: 'before',
      filterApplyonRenderData: 'vessel_id',
    },
    componentView: {
      gridComponent: 'bar',
      defaultComponent: 'grid',
    },
  },
};
