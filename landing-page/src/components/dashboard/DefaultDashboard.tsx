import React, { useState, useRef, useEffect, useContext } from 'react';
import { Col, Container, Row, Card, Modal, Form, Button, Alert } from 'react-bootstrap';
// @ts-ignore
import ReactHtmlParser from 'react-html-parser';
import { CallBackProps } from 'react-joyride';
import VesselList from './VesselList';
import { CustomOverlay, GuardWrapper } from '../common/index';
import TableauWidgets from './TableauWidgets';
import { KeycloakProps } from '../../types/keycloak';
import '../../pages/styles/dashboard.scss';
import { updateUserAttribute } from '../../services/user-service';
import * as spService from '../../services/sharepoint-service';
import NoAccess from './NoAccess';
import { hasAccessToVessel, hasKpiScorecardView, hasNovaView } from '../../utils/roles';
import { GlobalContext } from 'src/context/dashboard-context';
import CrewInjuryStatistics from './CrewInjuryStatistics';
import { WorldMap } from 'src/pages/WorldMap';
import { getWidgets } from '../../services/vm-widget-service';
import CardModuleContainer from './iCard/CardModuleContainer';
import { WidgetConstant } from './iCard/widget.constant';
import KpiCard, { KpiCardRef } from './KpiCard';
import { Widget } from 'src/types/types';

// Widget mapping function
const getWidgetComponent = (
  widgetName: string,
  keycloak: any,
  kpiCardRef: any,
  ship_party_id: any,
  crewInjuryStatRef: any,
  vesselListRef: any,
  fleetStoriesRef: any,
) => {
  switch (widgetName) {
    case 'World Clock':
      return <TableauWidgets keycloak={keycloak} />;
    case 'Navigation':
      return (
        <div className="report-container widget-border card">
          <WorldMap
            containerClassName="pl-0 pr-0"
            mapContainerStyle={{ height: '490px' }}
            keycloak={keycloak}
          />
        </div>
      );
    case 'Itinerary (ETA)':
      return <CardModuleContainer configKey={WidgetConstant.ITINERARY_ETA} />;
    case 'Surveys & Certificates':
      return (
        <CardModuleContainer
          configKey={WidgetConstant.SURVEYS_CERTIFICATES}
          onStatCardDataUpdate={(data) =>
            kpiCardRef.current?.handleStatCardDataUpdate('surveys', data)
          }
        />
      );
    case 'Owner Financial Reporting':
      return <CardModuleContainer configKey={WidgetConstant.OWNER_FINANCIAL_REPORTING} />;
    case 'Deficiencies':
      return (
        <CardModuleContainer
          configKey={WidgetConstant.DEFICIENCIES}
          onStatCardDataUpdate={(data) =>
            kpiCardRef.current?.handleStatCardDataUpdate('deficiencies', data)
          }
        />
      );
    case 'Vessels':
      return (
        <div className="h-100" ref={vesselListRef}>
          <GuardWrapper
            hasAccess={hasAccessToVessel(keycloak)}
            fallback={
              <Card body className="report-container widget-border">
                <p className="report-title" ref={fleetStoriesRef}>
                  Vessels
                </p>
                <NoAccess />
              </Card>
            }
          >
            <VesselList shipPartyId={ship_party_id} />
          </GuardWrapper>
        </div>
      );
    case 'FLEET Crew Statistics':
      return <CrewInjuryStatistics ref={crewInjuryStatRef} />;
    case 'Risk Assessment':
      return (
        <CardModuleContainer
          configKey={WidgetConstant.RISK_ASSESSMENT}
          onStatCardDataUpdate={(data) =>
            kpiCardRef.current?.handleStatCardDataUpdate('riskAssessments', data)
          }
        />
      );
    default:
      return null;
  }
};

function DefaultDashboard({ keycloak }: KeycloakProps) {
  const { ship_party_id } = keycloak.tokenParsed;
  const { ga4EventTrigger } = useContext(GlobalContext);
  const [run, setRun] = useState(!keycloak.tokenParsed.is_user_onboarded);
  const crewInjuryStatRef = useRef<{ crewStatistics: any }>(null);
  const vesselListRef = useRef(null);
  const fleetStoriesRef = useRef(null);
  const [tcData, setTCData] = useState<any>();
  const [checkTC, setCheckTC] = useState<boolean>(false);
  const [isTcLoading, setTcLoading] = useState<boolean>(true);
  const [modalShow, setModalShow] = useState<boolean>(false);
  const [widgets, setWidgets] = useState<Widget[]>([]);
  const [kpiCards, setKpiCards] = useState<Widget[]>([]);
  const kpiCardRef = useRef<KpiCardRef>(null);

  const sidebarMenuIcon = document.getElementById('sidebar-menu-icon');

  const hasNovaAccess = hasNovaView(keycloak);

  const isTcDone = !modalShow && !isTcLoading;

  const isTutorialReadyToStartWithNova = run && sidebarMenuIcon && isTcDone;

  const isTutorialReadyToStart = hasNovaAccess ? isTutorialReadyToStartWithNova : run && isTcDone;

  useEffect(() => {
    if (!keycloak.tokenParsed.is_user_onboarded) {
      setTimeout(() => {
        window.dispatchEvent(new Event('resize'));
      }, 1000);
    }

    getTermAndCondition();
  }, []);

  useEffect(() => {
    (async () => {
      try {
        const widgetsResponse = await getWidgets();
        const allItems = widgetsResponse.data.results;

        // Separate widgets (type 'W') and cards (type 'C')
        const widgetItems = allItems
          .filter((item) => item.type === 'W')
          .sort((a, b) => a.default_position - b.default_position);
        const cardItems = allItems
          .filter((item) => item.type === 'C')
          .sort((a, b) => a.default_position - b.default_position);

        setWidgets(widgetItems);
        setKpiCards(cardItems);
      } catch (error) {
        console.log(error);
      }
    })();
  }, []);

  useEffect(() => {
    if (!keycloak.tokenParsed.ship_party_id) {
      return;
    }
    if (
      tcData &&
      (!keycloak.tokenParsed.tc_version ||
        keycloak.tokenParsed.tc_version != parseInt(tcData.OData__UIVersionString, 10))
    ) {
      setModalShow(true);
    } else {
      setModalShow(false);
    }
  }, [keycloak.tokenParsed.tc_version, tcData, keycloak.tokenParsed.ship_party_id]);

  const updateTCVersionAttribute = (conditionVersion: any) => {
    try {
      setModalShow(false);
      (async function () {
        await updateUserAttribute(keycloak.tokenParsed.preferred_username, {
          attributes: {
            tc_version: parseInt(conditionVersion, 10),
          },
        });
        if (keycloak.tokenParsed.is_user_onboarded) window.location.reload();
      })();
    } catch (error) {
      console.log(error);
    }
  };

  const getTermAndCondition = async () => {
    try {
      const value = await spService.getTermCondition();
      setTCData(value.data.data);
      setTcLoading(false);
    } catch (error) {
      console.log(error);
    }
  };

  const checkedItem = (event: any) => {
    setCheckTC(event.target.checked);
  };

  const updateIsUserOnboardedAttribute = async () => {
    try {
      await updateUserAttribute(keycloak.tokenParsed.preferred_username, {
        attributes: {
          is_user_onboarded: true,
        },
      });
    } catch (error) {
      console.log(error);
    }
  };

  // const handleClickSidebarAutomatically = () => {
  //   const backdrop = document.getElementsByClassName('sideBar__modal') as HTMLCollectionOf<HTMLDivElement>;
  //   backdrop[0]?.click();
  // };

  const handleOverlayCallback = async (props: CallBackProps) => {
    const { status } = props;
    if (status === 'finished' || status === 'skipped') {
      console.log('onboarding finished');
      setRun(false);
      window.scrollTo(0, 0);
      await updateIsUserOnboardedAttribute();
    }
  };

  const getOverlays = () => {
    const crewStatisticRef = crewInjuryStatRef.current?.crewStatistics;
    const getOverlaySteps = [
      {
        target: '#main-home-dashboard-container',
        content: "Welcome to the New PARIS 2.0, see what's new!",
        disableBeacon: true,
        spotlightPadding: 0,
        placement: 'center' as const,
      },
      {
        target: '#crew-injury-stats-block',
        content:
          "Here's a snapshot of the crew injury statistics across all of FLEET's 600+ vessels",
        disableBeacon: true,
        spotlightPadding: 0,
        placement: 'bottom' as const,
      },
      {
        target: '#nova-demo-youtube-block',
        content: 'Watch a NOVA demo',
        disableBeacon: true,
        spotlightPadding: 0,
        placement: 'top' as const,
      },
    ];
    // sidebarMenuIcon
    const overlayStepsWithNova = [
      ...getOverlaySteps,
      {
        content: 'Introducing NOVA - our data analytics platform ',
        disableBeacon: true,
        spotlightPadding: 0,
        placement: 'top' as const,
        target: '#welcome-nova-banner-block',
        offset: -5,
        styles: {
          left: '0%',
        },
      },
    ];

    const overlaySteps = hasNovaView(keycloak) ? overlayStepsWithNova : getOverlaySteps;

    return (
      <>
        {crewStatisticRef && (
          <CustomOverlay
            run={run}
            steps={overlaySteps}
            floaterProps={{ hideArrow: true }}
            continuous
            handleOverlayCallback={handleOverlayCallback}
            styles={{
              options: {
                zIndex: 10000,
              },
            }}
          />
        )}
        {/* <Backdrop show>
          <div />
        </Backdrop> */}
      </>
    );
  };

  const visitNova = () => {
    if (ga4EventTrigger) ga4EventTrigger('NOVA Link Banner', 'NOVA Link Banner');
  };

  const novaLink = () =>
    hasKpiScorecardView(keycloak) ? (
      <>
        the{' '}
        <a href="/dashboard/kpi-scorecard" onClick={visitNova}>
          {' '}
          KPI Scorecard Analytics page.
        </a>
      </>
    ) : (
      <>
        our{' '}
        <a href="/nova" onClick={visitNova}>
          {' '}
          Data Analytics Platform NOVA.
        </a>
      </>
    );
  // Render widgets dynamically based on API response
  const renderWidgets = () => {
    const widgetRows = [];
    for (let i = 0; i < widgets.length; i += 2) {
      const widget1 = widgets[i];
      const widget2 = widgets[i + 1];

      widgetRows.push(
        <Row
          key={`widget-row-${i}`}
          className="justify-content-md-center tableau-widget transparent-no-pt"
        >
          <Col xs={12} md={6} className="py-3">
            {getWidgetComponent(
              widget1.name,
              keycloak,
              kpiCardRef,
              ship_party_id,
              crewInjuryStatRef,
              vesselListRef,
              fleetStoriesRef,
            )}
          </Col>
          {widget2 ? (
            <Col xs={12} md={6} className="py-3">
              {getWidgetComponent(
                widget2.name,
                keycloak,
                kpiCardRef,
                ship_party_id,
                crewInjuryStatRef,
                vesselListRef,
                fleetStoriesRef,
              )}
            </Col>
          ) : (
            <Col xs={12} md={6} className="py-3" />
          )}
        </Row>,
      );
    }
    return widgetRows;
  };

  return (
    <>
      <Modal
        show={modalShow}
        data-testid="term-condition-area"
        onHide={() => setModalShow(false)}
        aria-labelledby="confirmation-modal"
        dialogClassName="terms-condition-modal"
        centered
        backdrop="static"
        keyboard={false}
        scrollable
      >
        <Modal.Header>
          <Modal.Title id="confirmation-modal" style={{ borderBottom: '0' }}>
            PARIS 2.0 - Terms and Conditions
          </Modal.Title>
        </Modal.Header>
        <Modal.Body>{tcData && ReactHtmlParser(tcData.CanvasContent1)}</Modal.Body>
        <Modal.Footer style={{ borderTop: '0' }}>
          <Form.Check style={{ cursor: 'pointer' }}>
            <Form.Check.Input
              id="terms-condition-checkbox"
              type="checkbox"
              checked={checkTC}
              onChange={checkedItem}
            />
            <Form.Check.Label htmlFor="terms-condition-checkbox">
              Accept the conditions
            </Form.Check.Label>
          </Form.Check>
          <Button
            variant="secondary"
            disabled={!checkTC}
            onClick={() => {
              updateTCVersionAttribute(tcData.OData__UIVersionString);
            }}
          >
            Confirm
          </Button>
        </Modal.Footer>
      </Modal>
      <Container fluid className="vm-landing-page-container" id="main-home-vm-dashboard-container">
        {kpiCards.length > 0 && <KpiCard ref={kpiCardRef} kpiCards={kpiCards} />}
        {isTutorialReadyToStart && getOverlays()}
        <div className="justify-content-md-center banner">
          {hasNovaView(keycloak) && (
            <Alert variant="info" id="welcome-nova-banner-block">
              <span className="welcome-text-nova">
                Welcome to the new PARIS 2.0! Make sure you check out {novaLink()}
              </span>
            </Alert>
          )}
        </div>
        {renderWidgets()}
      </Container>
    </>
  );
}

export { DefaultDashboard };
